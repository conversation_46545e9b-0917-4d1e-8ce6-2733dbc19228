"use client"

import Link from "next/link"
import { Logo } from "@/components/ui/logo"

export function AppFooter() {
  return (
    <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
          {/* Logo and company info */}
          <div className="flex items-center gap-3">
            <Logo width={60} height={21} className="opacity-80" />
            <span className="text-xs text-muted-foreground hidden sm:inline">
              © {new Date().getFullYear()} Valencia Dr.
            </span>
          </div>

          {/* Links */}
          <div className="flex items-center gap-4 text-xs">
            <Link 
              href="/privacy" 
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Privacy
            </Link>
            <Link 
              href="/terms" 
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Terms
            </Link>
            <Link
              href="https://sga.formaloo.me/togeda-ai-contact-us"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Contact
            </Link>
          </div>

          {/* Mobile copyright */}
          <span className="text-xs text-muted-foreground sm:hidden">
            © {new Date().getFullYear()} Valencia Dr.
          </span>
        </div>
      </div>
    </footer>
  )
}
